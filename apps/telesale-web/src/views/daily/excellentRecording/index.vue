<!--
 * @Date         : 2025-04-23 18:34:55
 * @Description  : 优秀录音
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="g-margin-20">
    <el-card>
      <div>
        <!-- 搜索表单 -->
        <nexus-form ref="searchFormRef" v-model="searchForm" inline>
          <el-form-item prop="workerId">
            <AgentSelect
              v-model="searchForm.workerId"
              @change="onAgentChange"
            />
          </el-form-item>

          <el-form-item prop="callId">
            <el-input
              v-model.trim="searchForm.callId"
              placeholder="请输入通话ID"
              clearable
              @keyup.enter="onSearch"
            />
          </el-form-item>

          <el-form-item prop="duration">
            <el-input-number
              v-model="searchForm.minDuration"
              :min="0"
              :step="1"
              step-strictly
              placeholder="最小通话时长/s"
              :controls="false"
            />
            至
            <el-input-number
              v-model="searchForm.maxDuration"
              :min="0"
              :step="1"
              step-strictly
              placeholder="最大通话时长/s"
              :controls="false"
            />
          </el-form-item>

          <el-form-item prop="timeRange">
            <nexus-date-picker
              ref="timeRangePickerRef"
              v-model="searchForm.timeRange"
              type="daterange"
              :show-shortcuts="true"
              placeholder="请选择时间范围"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="onSearch">搜索</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </nexus-form>

        <!-- 数据表格 -->
        <nexus-table
          ref="TableRef"
          un-mounted
          :resFormat="data => data?.data"
          :get-list="getExcellentRecordingList"
          data-key="list"
        >
          <el-table-column label="通话ID" prop="actionId">
            <template #default="{ row }">
              <div class="flex items-center">
                <span>{{ row.actionId }}</span>
                <el-icon
                  class="ml-5px cursor-pointer text-primary"
                  title="复制通话ID"
                  @click="copyCallId(row.actionId)"
                >
                  <DocumentCopy />
                </el-icon>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" prop="createdAt">
            <template #default="{ row }">
              {{ formatDateTime(row.createdAt) }}
            </template>
          </el-table-column>

          <el-table-column label="坐席名称" prop="workerName" />

          <el-table-column label="所属小组" prop="lastGroupName" />

          <el-table-column label="通话时长" prop="callTimeLength">
            <template #default="{ row }">
              {{ durationChange(row.callTimeLength) }}
            </template>
          </el-table-column>

          <el-table-column label="评分总结" prop="aiAppraise.result">
            <template #default="{ row }">
              <div
                v-if="extractSummaryContent(row.aiAppraise?.result)"
                class="summary-content"
              >
                <el-tooltip
                  effect="dark"
                  placement="top"
                  :content="extractSummaryContent(row.aiAppraise?.result)"
                  raw-content
                  popper-class="summary-tooltip"
                  :disabled="!extractSummaryContent(row.aiAppraise?.result)"
                >
                  <div
                    class="summary-text"
                    v-html="extractSummaryContent(row.aiAppraise?.result)"
                  />
                </el-tooltip>
              </div>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link @click="viewCall(row)">
                查看通话
              </el-button>
            </template>
          </el-table-column>
        </nexus-table>
      </div>
    </el-card>

    <!-- 查看通话抽屉 -->
    <DetailDrawer v-model:visible="drawerVisible" :detail-info="currentRow" />
  </div>
</template>

<script setup lang="ts" name="excellentRecording">
// 优秀录音页面
import { ref, reactive, onMounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { DocumentCopy } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import durationChange from "/@/utils/handle/durationChange";
import NexusTable from "/@/components/Nexus/NexusTable/index.vue";
import NexusForm from "/@/components/Nexus/NexusForm/index.vue";
import NexusDatePicker from "/@/components/Nexus/NexusDatePicker/index.vue";
import DetailDrawer from "/@/views/aiQualityInspection/excellentCases/DetailDrawer/index.vue";
import AgentSelect from "/@/components/AgentSelect/index.vue";
import {
  getExcellentCasesList,
  type ExcellentCasesQueryParams
} from "/@/api/AIQualityInspection/excellentCases";

// 页面状态
const TableRef = ref();
const searchFormRef = ref();
const timeRangePickerRef = ref();
const drawerVisible = ref(false);
const currentRow = ref();

// 搜索表单数据
const searchForm = reactive({
  workerId: undefined, // 坐席ID
  callId: "", // 通话ID
  minDuration: undefined, // 最小通话时长
  maxDuration: undefined, // 最大通话时长
  timeRange: null // 时间范围
});

// 获取优秀录音列表的API调用函数
const getExcellentRecordingList = async (params: any) => {
  // 构建API查询参数
  const apiParams: ExcellentCasesQueryParams = {
    // 默认筛选条件：只显示A级评分且人工复检通过的优秀案例
    aiAppraiseScore: "A", // AI评级为A级
    humanAppraiseResult: "通过" // 人工复检结果为通过
  };

  // 映射用户搜索参数
  if (params.callId) {
    apiParams.actionId = params.callId;
  }

  if (params.workerId) {
    apiParams.workerId = params.workerId;
  }

  if (params.startTime && params.endTime) {
    // 时间戳已经是毫秒级，getExcellentCasesList函数会自动转换为秒级
    apiParams.beginTime = params.startTime;
    apiParams.endTime = params.endTime;
  }

  // 通话时长参数
  if (params.minDuration !== undefined && params.minDuration !== null) {
    apiParams.minDuration = params.minDuration;
  }

  if (params.maxDuration !== undefined && params.maxDuration !== null) {
    apiParams.maxDuration = params.maxDuration;
  }

  // 分页参数
  if (params.pages) {
    apiParams.pages = params.pages;
  }

  if (params.pageSize) {
    apiParams.pageSize = params.pageSize;
  }

  return await getExcellentCasesList(apiParams);
};

// 格式化日期时间
const formatDateTime = (timestamp: number | string) => {
  if (!timestamp) return "-";
  return dayjs(timestamp).format("YYYY-MM-DD HH:mm:ss");
};

// 提取评分总结内容
const extractSummaryContent = (content: string) => {
  if (!content) return "";

  // 定义要匹配的关键词列表，只匹配评估总结
  const keywords = ["**评估总结**", "评估总结"];

  let summaryStartIndex = -1;
  let keywordLength = 0;

  // 按优先级查找关键词
  for (const keyword of keywords) {
    summaryStartIndex = content.indexOf(keyword);
    if (summaryStartIndex !== -1) {
      keywordLength = keyword.length;
      break;
    }
  }

  if (summaryStartIndex === -1) {
    // 如果都没有找到，返回空字符串
    return "";
  }

  // 找到关键词后面的内容
  const summaryStart = summaryStartIndex + keywordLength;
  let summaryContent = content.substring(summaryStart).trim();

  // 移除开头的冒号（中文和英文）、换行符和HTML标签
  summaryContent = summaryContent
    .replace(/^[：:]+/g, "")
    .replace(/^(<br\s*\/?>|\s)+/i, "");

  // 查找下一个结束标记的位置，但要更智能地处理
  // 我们要找的是下一个主要部分的开始，而不是内容中的格式标记
  const endMarkers = ["###", "8. **", "9. **", "10. **"];
  let nextSectionIndex = -1;

  for (const marker of endMarkers) {
    const index = summaryContent.indexOf(marker);
    if (index !== -1 && (nextSectionIndex === -1 || index < nextSectionIndex)) {
      nextSectionIndex = index;
    }
  }

  if (nextSectionIndex !== -1) {
    summaryContent = summaryContent.substring(0, nextSectionIndex).trim();
  }

  // 如果内容太短，可能是被错误截断了，尝试获取更多内容
  if (summaryContent.length < 50) {
    // 重新获取，不使用结束标记
    summaryContent = content.substring(summaryStart).trim();
    summaryContent = summaryContent
      .replace(/^[：:]+/g, "")
      .replace(/^(<br\s*\/?>|\s)+/i, "");
  }

  return summaryContent;
};

// 复制通话ID
const copyCallId = (callId: string) => {
  navigator.clipboard.writeText(callId).then(
    () => {
      ElMessage.success("通话ID已复制到剪切板");
    },
    () => {
      ElMessage.error("复制失败");
    }
  );
};

// 坐席变更
const onAgentChange = (value: any) => {
  searchForm.workerId = value;
};

// 查看通话详情
const viewCall = (row: any) => {
  // 提取当前行的评分总结内容
  const summaryContent = extractSummaryContent(row.aiAppraise?.result);

  // 将行数据和提取的评分总结一起传递给DetailDrawer
  currentRow.value = {
    ...row,
    extractedSummary: summaryContent // 添加提取的评分总结内容
  };
  drawerVisible.value = true;
};

onMounted(() => {
  // 设置默认时间范围为当月
  const now = dayjs();
  const startOfMonth = now.startOf("month");
  const endOfMonth = now.endOf("month");
  searchForm.timeRange = [startOfMonth, endOfMonth];

  nextTick(() => {
    // 初始化时执行搜索
    onSearch();
  });
});

// 搜索功能
const onSearch = () => {
  // 构建完整的参数对象，包含所有可能的字段
  // 这样可以确保旧参数被完全覆盖
  const params: any = {
    workerId: undefined,
    callId: undefined,
    minDuration: undefined,
    maxDuration: undefined,
    startTime: undefined,
    endTime: undefined
  };

  // 坐席ID参数
  if (searchForm.workerId) {
    params.workerId = searchForm.workerId;
  }

  if (searchForm.callId) {
    params.callId = searchForm.callId; // 这会在API函数中映射为actionId
  }

  // 通话时长参数
  if (searchForm.minDuration !== undefined && searchForm.minDuration !== null) {
    params.minDuration = searchForm.minDuration;
  }

  if (searchForm.maxDuration !== undefined && searchForm.maxDuration !== null) {
    params.maxDuration = searchForm.maxDuration;
  }

  // 时间范围参数处理
  if (timeRangePickerRef.value?.unixsDatePicker) {
    const [startTime, endTime] = timeRangePickerRef.value.unixsDatePicker;
    if (startTime && endTime) {
      params.startTime = startTime;
      params.endTime = endTime;
    }
  }
  TableRef.value?.search(params);
};

// 重置表单
const resetForm = () => {
  // 重置表单字段
  searchForm.workerId = undefined;
  searchForm.callId = "";
  searchForm.minDuration = undefined;
  searchForm.maxDuration = undefined;

  // 重置为当月时间
  const now = dayjs();
  const startOfMonth = now.startOf("month");
  const endOfMonth = now.endOf("month");
  searchForm.timeRange = [startOfMonth, endOfMonth];

  // 使用 nextTick 确保时间选择器的值已经更新
  nextTick(() => {
    // 执行搜索，现在 onSearch 会构建包含所有字段的完整参数对象
    onSearch();
  });
};
</script>

<style scoped>
.excellent-recording-container {
  padding: 20px;
}

.search-form-container {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.summary-content {
  width: 100%;
}

.summary-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  line-height: 1.4;
  max-width: 100%;
}

.summary-text:hover {
  color: #409eff;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-primary {
  color: #409eff;
}

.text-primary:hover {
  color: #66b1ff;
}

.cursor-pointer {
  cursor: pointer;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-5px {
  margin-left: 5px;
}

.mb-20px {
  margin-bottom: 20px;
}

.mt-20px {
  margin-top: 20px;
}

/* 限制评分总结tooltip的宽度 */
:deep(.summary-tooltip) {
  max-width: 400px !important;
}
</style>

<style lang="scss">
/* 全局样式 - 限制评分总结tooltip的宽度 */
.summary-tooltip {
  max-width: 400px !important;
  word-wrap: break-word !important;
  white-space: normal !important;
}

/* 更强的选择器 */
.el-popper.summary-tooltip {
  max-width: 400px !important;
}

/* 针对Element Plus的tooltip内容 */
.summary-tooltip .el-tooltip__content {
  max-width: 400px !important;
  word-wrap: break-word !important;
  white-space: normal !important;
}
</style>
