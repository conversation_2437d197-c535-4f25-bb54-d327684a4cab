/*
 * @Date         : 2025-06-26 14:29:42
 * @Description  : 补差价处理数据
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import dayjs from "dayjs";
import { discountGoodsList } from "./data";

export const diffDataHandle = item => {
  item.reducePrice = item.originalAmount - item.amount;

  item.directAmount =
    discountGoodsList.find(direct => direct.id === item.skuGoodId)?.amount || 0;

  item.diffPrice =
    item.originalAmount -
    item.deductAmount -
    item.reducePrice -
    item.directAmount;
  if (item.diffPrice < 0) {
    item.diffPrice = 0;
  }

  const notDeductList = item.orderInterceptReason.map(order => {
    return {
      id: order.fromId,
      name: order.fromName,
      paidTime: order.paidTime,
      reason: order.reason,
      deductAmount: 0,
      deductCategory: "none"
    };
  });

  // 7天外的总金额
  // item.deductibleOrders 通过paidTime时间倒序
  item.deductibleOrders.sort((a, b) => {
    return new Date(b.paidTime).getTime() - new Date(a.paidTime).getTime();
  });
  let outOf7DaysAmount = 0;

  item.deductibleOrders.forEach(ded => {
    // 判断a.paidTime是否在7天内
    if (dayjs().diff(dayjs(ded.paidTime), "day") > 7) {
      outOf7DaysAmount += ded.deductAmount;
    }
  });

  item.deductibleOrders.forEach(ded => {
    if (dayjs().diff(dayjs(ded.paidTime), "day") > 7) {
      ded.reason = `该订单为7天外订单，补差直降优惠+7天外订单抵扣优惠之和为${
        outOf7DaysAmount > item.gt7OrdersMaxDeductAmount
          ? item.gt7OrdersMaxDeductAmount?.toFixed?.(2)
          : outOf7DaysAmount?.toFixed?.(2)
      }`;
    }
  });

  item.deductibleOrders = item.deductibleOrders.concat(notDeductList);
};
