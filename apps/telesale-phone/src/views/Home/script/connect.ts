import { invoke, event } from '@tauri-apps/api'
import { ASR_CONFIG } from '@/const/ASR'
import { ACCOUNT_TIANYAN, ACCOUNT_ZHIYU } from '@/const/Account'
import { useUser } from '@/store/user'

export default function() {
  const userStore = useUser()
  let account = {
    worker_id: userStore.workerId,
    user: userStore.qmExten,
    password: userStore.passwordFormat,
    domain: userStore.domain,
    isasr: userStore.isasr,
    host: userStore.sipHost
  }

  if (userStore.channel === '百悟') {
    if (import.meta.env.VITE_ENV === 'master') {
      account.user = '*********' + account.user
    } else {
      account.user = '*********' + account.user
    }
  }

  console.log('account', account)

  invoke('handle_login', {
    account: account, asrConfig: ASR_CONFIG
  })
    .then()
    .catch(err => {
      console.log('login failed: err is ', err)
    })
}
