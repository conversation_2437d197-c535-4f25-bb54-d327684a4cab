<script setup lang="ts">
import { ref } from "vue";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import { videoHistory } from "/@/api/customerDetails";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStoreHook } from "/@/store/modules/user";
import timeChange from "/@/utils/handle/timeChange";
import { getLabel } from "/@/utils/common";
import { videoTypeList } from "../../data";
import { useTable } from "/@/hooks/useTable";

let device = useAppStoreHook().device;
interface Props {
  customMsg: any;
}

const props = defineProps<Props>();

const listHeader = [
  {
    field: "semesterName",
    desc: "学期",
    filters: row => row.semesterName || ""
  },
  {
    field: "subjectName",
    desc: "学科",
    filters: row => row.subjectName || ""
  },
  {
    field: "publisherName",
    desc: "教材",
    filters: row => row.publisherName || ""
  },
  {
    field: "videoName",
    desc: "视频名称",
    filters: row => row.video?.name || "",
    minWidth: 160
  },
  {
    field: "videoType",
    desc: "视频类型",
    minWidth: 160,
    customRender: ({ text, row }) => {
      const list = [];
      row.packageInfos?.forEach(item => {
        const value = getLabel(item.type, videoTypeList);
        if (!list.includes(value)) {
          list.push(value);
        }
      });

      return text === "system" ? "" : list.join("、");
    }
  },
  {
    field: "topicName",
    desc: "知识点名称",
    filters: row => row.topic?.name || "",
    minWidth: 160
  },
  {
    field: "videoName",
    desc: "是否付费内容",
    filters: row =>
      row.topic?.isFreeTime ? "限免" : row.topic?.pay ? "付费" : "免费",
    minWidth: 160
  },
  { field: "timestamp", desc: "观看时间", timeChange: 2, minWidth: 120 }
];

const { loading, dataList, onSearch, Pagination } = useTable({
  api: videoHistory,
  immediate: false,
  initParams: {
    userId: props.customMsg.userid
  }
});

props.customMsg.userid && onSearch();
</script>

<template>
  <div v-loading="loading">
    <div
      v-if="
        !customMsg.videoid &&
        !customMsg.topicId &&
        customMsg.triggerEvent?.practiceType
      "
      class="d-tip-box"
    >
      <IconifyIconOffline icon="information-line" style="font-size: 25px" />
      <span>
        该条线索的触发场景：{{ customMsg.triggerEvent?.practiceType
        }}{{ customMsg.triggerEvent?.practiceScene ? "-" : ""
        }}{{ customMsg.triggerEvent?.practiceScene }}；所学学科为{{
          useUserStoreHook().subjectList[
            customMsg.triggerEvent?.subjectId - 1
          ] || "未知"
        }}；触发时间： {{ timeChange(customMsg.receiveTime, 2) }}
      </span>
    </div>
    <template v-if="device !== 'mobile'">
      <ReTable :dataList="dataList" :listHeader="listHeader" />
    </template>
    <template v-else>
      <ReCardList
        :dataList="dataList"
        :listHeader="listHeader"
        :isCardBox="false"
      />
    </template>
    <div class="mt-10px">
      <Pagination />
    </div>
  </div>
</template>
<style scoped>
.d-tip-box {
  background-color: #eaf5ff;
  font-weight: bold;
  padding: 10px 20px;
  margin-bottom: 20px;
  font-size: 15px;
  line-height: 1.7;
}
</style>
