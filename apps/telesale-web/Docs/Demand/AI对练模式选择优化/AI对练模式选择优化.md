# AI对练模式选择优化

## 文档内容总结

本文档描述了AI对练模式的优化需求，主要针对当前语音流模式耗时过长和机器人预设信息差问题进行改进。

**核心目标：** 增加纯文本AI对练模式，提高对练工作效率，优化机器人预设配置

**主要变更内容：**
- **新增功能：** 增加纯文本对练模式选择
- **交互优化：** 在任务选择页面增加模式选择弹窗
- **机器人优化：** 创建机器人时仅选择问题，不提供参考答案
- **界面调整：** 纯文本模式下取消AI语音合成和播放组件

**技术要点：** 前端交互优化、AI语音合成控制、机器人预设数据结构调整

---

## 一、需求概述

### 现状
目前业务验证下来，当前语音流的模式会导致一线业务耗时需要等待耗时很久，基于业务时效性要求考虑，希望增加纯文本返回对练模式。

当前在设置机器人预设过程中，由于提供给AI的参考资料模式为问题+答案，导致机器人没有信息差：会直接问道你们有没有周年庆活动/初升高衔接课包等。

### 需求目标
- 增加纯文本AI对练模式，提高对练工作效率
- 创建机器人的预设，选择问题后，取消参考答案的提交，仅提供问题名称作为参考资料

### 需求设计关键点
模式选择和对练交互

## 二、调整范围

| 功能名称 | 类型 | 补充说明 |
|---------|------|----------|
| 练习-纯文本对练 | 新增功能 | 增加纯文本对练模式 |

## 三、功能需求描述

### 任务选择

#### 我的任务
**任务选择-功能说明：**
- 点击练习按钮
- 打开AI练习模式弹窗选择

#### AI练习模式选择弹窗说明：

**默认选项：**
- 语音对练模式（当前模式）
- 纯文本对练模式（新增）

**弹窗交互：**
- 用户选择对练模式后，进入对应的练习界面
- 语音对练模式：保持现有功能不变
- 纯文本对练模式：取消AI语音合成，直接显示文本回复

### 纯文本对练模式

#### 界面调整
- 取消AI语音合成功能
- 取消音频播放组件
- AI回复直接以文本形式展示
- 保持其他交互逻辑不变

#### 机器人预设优化
- 创建机器人时，仅选择问题作为参考资料
- 不再提供参考答案
- 增加机器人的信息差，提高对练真实性

## 四、技术实现要点

### 前端交互
- 新增模式选择弹窗组件
- 根据选择的模式控制AI语音合成开关
- 纯文本模式下隐藏音频相关组件

### 数据结构
- 机器人预设数据结构调整
- 仅保存问题信息，移除答案字段

### 性能优化
- 纯文本模式下减少AI语音合成API调用
- 提高对练响应速度

## 五、验收标准

1. 用户可以在练习前选择对练模式
2. 纯文本模式下AI回复以文本形式直接展示
3. 语音模式保持原有功能不变
4. 机器人预设仅包含问题信息
5. 纯文本模式响应速度明显提升

## 六、风险评估

### 技术风险
- 前端组件改造可能影响现有功能
- 数据结构调整需要兼容性处理

### 业务风险
- 用户习惯改变需要适应期
- 纯文本模式可能影响练习效果

## 七、上线计划

1. 开发阶段：前端交互优化、后端数据结构调整
2. 测试阶段：功能测试、兼容性测试
3. 灰度发布：小范围用户试用
4. 全量发布：正式上线新功能
