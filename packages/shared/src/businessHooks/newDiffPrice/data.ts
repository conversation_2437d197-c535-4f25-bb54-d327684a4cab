/*
 * @Date         : 2025-01-21 16:14:16
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

export const diffPriceColunms = [
  {
    field: "skuGoodName",
    desc: "目标商品",
    htmlChange: true,
    minWidth: 170
  },
  {
    field: "originalAmount",
    desc: "商品原价（划线价）",
    minWidth: 80,
    customRender: ({ text }) => "¥ " + text.toFixed(2)
  },

  {
    field: "reducePrice",
    desc: "商品直降",
    minWidth: 50,
    customRender: ({ text }) => "¥ " + text.toFixed(2)
  },

  {
    field: "deductAmount",
    desc: "补差优惠合计",
    minWidth: 60,
    slot: {
      name: "deductAmount"
    }
  },

  {
    field: "directAmount",
    desc: "暑促优惠",
    minWidth: 60,
    customRender: ({ text }) => {
      return "¥ " + text.toFixed(2);
    }
  },

  {
    field: "diffPrice",
    desc: "实付",
    customRender: ({ text }) => "¥ " + text.toFixed(2),
    minWidth: 50
  }
];

const discountGoodsMap = {
  default: [
    {
      name: "小学数学全阶课包（学至毕业）",
      id: "17d8dff8-5f79-4202-a0d1-40865e6509b1",
      amount: 400
    }
  ],
  stage: [],
  master: [
    {
      name: "小学数学全阶课包（学至毕业）",
      id: "617ac65e-b7f9-42f5-9831-d51c6d7af43c",
      amount: 0
    },
    {
      name: "同步优学课包（4年）",
      id: "a189c523-aef7-434a-b54f-e4c63888c847",
      amount: 300
    },
    {
      name: "培优提分课包（中考30版）",
      id: "9f5c1509-89bd-4cbf-b8e7-2ac4e0821d43",
      amount: 500
    },
    {
      name: "培优提分课包（中考29版）",
      id: "4f5e89ef-4fab-4dc9-ab0b-e8a42c7b7cc4",
      amount: 500
    },
    {
      name: "培优提分课包（中考28版）",
      id: "bb0789ec-2d38-4523-a474-3661bd8d4044",
      amount: 500
    },
    {
      name: "培优提分课包（中考27版）",
      id: "93843451-32f5-49a9-940b-0cbf11786cc6",
      amount: 500
    },
    {
      name: "培优提分课包（中考26版）",
      id: "9ced7ca6-cc57-4662-aa4b-2713ef434e72",
      amount: 500
    },
    {
      name: "培优提分课包（高考28版）",
      id: "e287656e-c20a-431b-9cc3-34c78b0ee5de",
      amount: 500
    },
    {
      name: "培优提分课包（高考27版）",
      id: "d5b2e71e-9fa0-414c-8653-6eab969192df",
      amount: 500
    },
    {
      name: "培优提分课包（高考26版）",
      id: "f9015dd2-0d9f-410f-bb68-dca6332d9928",
      amount: 500
    },
    {
      name: "小初全阶课包（小数全阶+中考31版）",
      id: "6a88e11a-f280-42c4-8470-5fa76c58180a",
      amount: 0
    },
    {
      name: "初高培优提分课包（中考28版高考31版）",
      id: "9d92403a-99a6-4a67-a9a3-31e8093441ec",
      amount: 500
    },
    {
      name: "初高培优提分课包（中考27版高考30版）",
      id: "0ae2b2de-b9db-4f17-8d63-bdd940326e6c",
      amount: 500
    },
    {
      name: "初高培优提分课包（中考26版高考29版）",
      id: "cef59d20-6ed3-4b65-85d0-87bc37182a60",
      amount: 500
    },
    {
      name: "小初高全阶课包（34版）",
      id: "8a8a92e5-c671-4e0c-a353-bd53de770a1b",
      amount: 0
    }
  ]
};

// 优惠商品列表
export const discountGoodsList =
  discountGoodsMap[import.meta.env.MODE] || discountGoodsMap["default"];
