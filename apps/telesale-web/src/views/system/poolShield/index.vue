<script setup lang="ts" name="qualityCheckDurationSet">
import InvestmentProducts from "./components/InvestmentProducts.vue";
import ReturnUsers from "./components/ReturnUsers.vue";
import ServicePeriod from "./components/ServicePeriod.vue";
</script>

<template>
  <div class="g-margin-20">
    <el-alert
      title="注意：所有配置修改，将在保存的五分钟后生效！"
      type="error"
      :closable="false"
    />
    <el-card>
      <ReturnUsers />
    </el-card>
    <el-card class="mt-20px">
      <InvestmentProducts />
    </el-card>
    <el-card class="mt-20px">
      <ServicePeriod />
    </el-card>
  </div>
</template>
