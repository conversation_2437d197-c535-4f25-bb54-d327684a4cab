<script setup lang="ts" name="beyond">
import { ref } from "vue";
import LearnReport from "./components/LearnReport.vue";
import { getAuth } from "/@/utils/auth";
import { ElMessage, FormInstance } from "element-plus";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { onionIdReg, phoneReg } from "/@/utils/common/pattern";
import { findUser, order } from "/@/api/customerDetails";
import timeChange from "/@/utils/handle/timeChange";
import {
  AllocationRes,
  findReferral,
  getAllocationApi,
  phoneInfo,
  WorkerInfo
} from "/@/api/customer";
import { temaList } from "./data/index";
import { getLabel } from "/@/utils/common";
import AuthList from "../details/components/tabs/AuthList.vue";
import OrderList from "../details/components/tabs/OrderList.vue";
import VedioList from "../details/components/tabs/VedioList.vue";
import AiPlan from "../details/components/tabs/AiPlan.vue";
import OperateBtns from "./components/OperateBtns.vue";
import DiffPrice from "/@/businessComponents/DiffPirce/index.vue";

import { DocumentCopy } from "@element-plus/icons-vue";
import { useClipboard } from "@vueuse/core";
import { createWechatLinkApi } from "/@/api/customer/beyond";
import {
  getDiscoveryPageAuthApi,
  getAbLearningApi
} from "/@/api/customer/details";
import { getQualityUserApi } from "@telesale/server/src/api/customer/details";
import Renew from "../details/components/tabs/Renew.vue";

const activeStage = ref<string>("learnReport");
const loading = ref<boolean>(false);
const formRef = ref<FormInstance>();
const form = ref({
  onionId: "",
  phone: ""
});
const rules = {
  onionId: {
    message: "洋葱ID格式错误",
    trigger: "blur",
    pattern: onionIdReg
  },
  phone: {
    message: "手机号格式错误",
    trigger: "blur",
    pattern: phoneReg
  }
};

const userInfo = ref();
const transterInfo = ref<any>({});
const serverInfo = ref<Partial<AllocationRes>>();
const workerInfo = ref<Partial<WorkerInfo>>({});
const link = ref<string>("");
const linkLoading = ref<boolean>(false);
const discoveryPageAuth = ref<boolean>(false);
const studyPageAuth = ref<boolean>(false);
const specialOldUsers = ref<boolean>(false);
const { copy } = useClipboard();

const onSearch = () => {
  formRef.value.validate(async valid => {
    if (valid) {
      if (!form.value.onionId && !form.value.phone) {
        return ElMessage.warning("请输入查询条件");
      }
      if (form.value.onionId && form.value.phone) {
        return ElMessage.warning("洋葱ID和手机号只能查询一个");
      }
      loading.value = true;
      try {
        const key = form.value.onionId ? "onionId" : "phone";
        const { data: user } = await findUser({
          key: key,
          value: form.value[key]
        });
        userInfo.value = user;
      } catch {
        loading.value = false;
        userInfo.value = {};
        transterInfo.value = {};
        serverInfo.value = {};
        workerInfo.value = {};
        return;
      }

      if (!userInfo.value.id) return;

      await getUserWorker(userInfo.value);

      await getExperimental();

      await getStudyPage();

      await getSpecialOldUsers();

      if (getAuth("telesale_admin_custom_transferFind")) {
        await getTransferData(userInfo.value);
      }

      loading.value = false;
    }
  });
};

const getTransferData = async user => {
  try {
    const { data: transter } = await findReferral({ onionId: user.onionId });
    transterInfo.value.onionId = transter.onionId;
    transterInfo.value.grade = transter.grade;
    transterInfo.value.userId = transter.userId;
  } catch {
    transterInfo.value.onionId = undefined;
    transterInfo.value.grade = undefined;
    transterInfo.value.userId = undefined;
  }

  if (!transterInfo.value.onionId) return;
  try {
    const { data: orderInfo } = await order({
      userid: transterInfo.value.userId
    });
    for (let i = 0; i < orderInfo.length; i++) {
      if (
        orderInfo[i].status === "支付成功" ||
        orderInfo[i].status === "退款成功"
      ) {
        transterInfo.value.name = orderInfo[i].good.name;
        transterInfo.value.paidTime = orderInfo[i].paidTime;
        transterInfo.value.originalAmount = orderInfo[i].good.amount;
        transterInfo.value.status = orderInfo[i].status;
        break;
      }
    }
  } catch {
    transterInfo.value.name = "";
    transterInfo.value.paidTime = "";
    transterInfo.value.originalAmount = "";
    transterInfo.value.status = "";
  }
};

const getUserWorker = async user => {
  if (getAuth("telesale_admin_custom_beyond_allocation")) {
    try {
      const { data } = await getAllocationApi({
        onionId: user.onionId
      });
      data.allocations = data.allocations?.filter(
        item => item.state === "effective"
      );
      serverInfo.value = data;
    } catch {
      loading.value = false;
      serverInfo.value = {};
    }
  }

  try {
    const { data: worker } = await phoneInfo({ onionId: user.onionId });
    workerInfo.value.workerName = worker.workerName;
    workerInfo.value.workerid = worker.workerid;
    workerInfo.value.firstValidDial = worker.firstValidDial;
    workerInfo.value.grade = worker.grade;
    workerInfo.value.stage = worker.stage;
    workerInfo.value.featureTags = worker.featureTags;
  } catch {
    workerInfo.value = {};
  }
};

const onReset = () => {
  form.value = {
    onionId: "",
    phone: ""
  };
  userInfo.value = {};
  transterInfo.value = {};
  serverInfo.value = {};
  workerInfo.value = {};
};

const getExperimental = async () => {
  const { data } = await getDiscoveryPageAuthApi({ userId: userInfo.value.id });
  discoveryPageAuth.value = data.hasAuth;
};

const getStudyPage = async () => {
  const { data } = await getAbLearningApi({ userId: userInfo.value.id });
  studyPageAuth.value = data.isA;
};

const getSpecialOldUsers = async () => {
  const { data } = await getQualityUserApi({ userId: userInfo.value.id });
  specialOldUsers.value = data.ok;
};

const getLink = () => {
  if (link.value) {
    copy(link.value);
    ElMessage.success("复制成功");
    return;
  }
  linkLoading.value = true;
  createWechatLinkApi()
    .then(res => {
      link.value = res.data.link;
      copy(link.value);
      ElMessage.success("复制成功");
    })
    .finally(() => {
      linkLoading.value = false;
    });
};
</script>
<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card class="min-h-300px">
      <el-form
        class="d-form"
        ref="formRef"
        :inline="true"
        :model="form"
        :rules="rules"
        @submit.prevent
      >
        <el-form-item prop="onionId">
          <el-input
            v-model="form.onionId"
            placeholder="请输入客户洋葱ID"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item prop="phone">
          <el-input
            v-model="form.phone"
            placeholder="请输入客户手机号"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('search')"
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button :icon="useRenderIcon('refresh')" @click="onReset">
            重置
          </el-button>
        </el-form-item>
        <el-form-item class="g-set-button">
          <el-tooltip
            content="该链接可以发给任何满足转介绍条件的用户，用户点击链接进入小程序与销售绑定关系后分享活动给新用户。"
            placement="bottom"
            effect="dark"
            popper-class="w-200px"
          >
            <el-button
              type="primary"
              :loading="linkLoading"
              @click="getLink"
              v-auth="'telesale_admin_custom_transferPoster'"
            >
              获取转介绍链接
            </el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>
      <template v-if="userInfo?.id">
        <OperateBtns
          :userId="userInfo.id"
          :onionId="userInfo.onionId"
          :phone="userInfo.phone"
          :grade="workerInfo.grade"
          :stage="workerInfo.stage"
        />
        <el-descriptions title="基础信息" :column="4" border>
          <el-descriptions-item label="洋葱ID">
            {{ userInfo?.onionId }}
            <el-icon
              v-if="userInfo.onionId"
              class="cursor-pointer inline-block mt-2px"
              v-copy="userInfo.onionId"
              size="17"
            >
              <DocumentCopy />
            </el-icon>
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ userInfo?.phone }}
            <el-icon
              v-if="userInfo.phone"
              class="cursor-pointer inline-block mt-2px"
              v-copy="userInfo.phone"
              size="17"
            >
              <DocumentCopy />
            </el-icon>
          </el-descriptions-item>
          <el-descriptions-item label="归属坐席">
            {{ workerInfo?.workerName }}
          </el-descriptions-item>
          <el-descriptions-item label="有效通话">
            {{ workerInfo?.firstValidDial ? "有" : "无" }}
          </el-descriptions-item>
          <el-descriptions-item
            label="当前服务期状态"
            v-if="getAuth('telesale_admin_custom_beyond_allocation')"
          >
            <template v-if="serverInfo?.allocations?.length > 0">
              <div
                v-for="(item, i) in serverInfo.allocations"
                :key="i"
                style="margin-top: 10px"
              >
                <span style="margin-right: 10px">
                  {{ getLabel(item.teamId, temaList) }}
                </span>
                <span>
                  {{ timeChange(item.startTime, 3) }} ～
                  {{ timeChange(item.endTime, 3) }}
                </span>
              </div>
            </template>
            <div class="m-0!" v-else>
              <span v-if="serverInfo?.userId">无</span>
            </div>
          </el-descriptions-item>

          <el-descriptions-item label="实验组信息" :span="2">
            <div class="auth-info">
              <span class="c-blue font-bold" v-if="specialOldUsers">
                用户分层测试A
              </span>
              <span class="c-#e6a23c font-bold" v-if="discoveryPageAuth">
                发现页实验组
              </span>
              <span class="c-red font-bold" v-if="studyPageAuth">
                学习页实验组
              </span>
              <span
                class="c-blue font-bold"
                v-if="workerInfo.featureTags?.includes(18)"
              >
                已购108商品
              </span>
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions
          title="转介绍信息"
          :column="4"
          border
          class="mt-20px"
          v-if="transterInfo.onionId"
          v-auth="'telesale_admin_custom_transferFind'"
        >
          <el-descriptions-item label="推荐人洋葱ID">
            {{ transterInfo?.onionId }}
          </el-descriptions-item>
          <el-descriptions-item label="推荐人年级">
            {{ transterInfo?.grade }}
          </el-descriptions-item>
          <el-descriptions-item label="商品名称">
            {{ transterInfo?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="订单总价">
            {{ transterInfo?.originalAmount }}
          </el-descriptions-item>
          <el-descriptions-item label="支付时间">
            {{ timeChange(transterInfo?.paidTime, 3) }}
          </el-descriptions-item>
          <el-descriptions-item label="支付状态">
            {{ transterInfo?.status }}
          </el-descriptions-item>
        </el-descriptions>
        <el-tabs
          type="border-card"
          v-model="activeStage"
          class="mt-20px"
          :key="userInfo?.id || 'tab-key'"
        >
          <el-tab-pane
            label="学情报告"
            name="learnReport"
            v-if="getAuth('telesale_admin_custom_learnReport')"
          >
            <LearnReport :userId="userInfo?.id" />
          </el-tab-pane>
          <el-tab-pane
            label="内容权限"
            name="contentLimit"
            v-if="getAuth('telesale_admin_custom_beyond_contentAuth')"
            lazy
          >
            <AuthList :userid="userInfo?.id" />
          </el-tab-pane>
          <el-tab-pane
            label="订单记录"
            name="orderRecord"
            v-if="getAuth('telesale_admin_custom_beyond_orderRecord')"
            lazy
          >
            <OrderList :userid="userInfo?.id" />
          </el-tab-pane>
          <el-tab-pane
            label="观看记录"
            name="lookRecord"
            v-if="getAuth('telesale_admin_custom_beyond_vedioList')"
            lazy
          >
            <VedioList :customMsg="{ userid: userInfo?.id }" />
          </el-tab-pane>
          <el-tab-pane
            label="AI定制班"
            name="studyPlan"
            v-if="getAuth('telesale_admin_customer_studyPlan')"
            lazy
          >
            <AiPlan :userId="userInfo?.id" :onionId="userInfo.onionId" />
          </el-tab-pane>
          <el-tab-pane
            label="补差价查询（新）"
            name="diffPrice"
            v-if="getAuth('telesale_admin_diff_price')"
            lazy
          >
            <DiffPrice :userId="userInfo?.id" />
          </el-tab-pane>
          <el-tab-pane
            label="续购"
            name="purchase"
            v-if="getAuth('telesale_admin_repurchase')"
            lazy
          >
            <Renew :userid="userInfo?.id" isShowOperation type="repurchase" />
          </el-tab-pane>
        </el-tabs>
      </template>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.auth-info {
  & > span:not(:last-child)::after {
    content: "，";
    width: 10px;
    height: 10px;
    display: inline-block;
  }
}
</style>
