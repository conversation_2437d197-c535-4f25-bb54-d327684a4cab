/*
 * @Date         : 2025-01-21 16:10:14
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { cloneDeep } from "lodash-es";
import { ref } from "vue";
import dayjs from "dayjs";
import { getDiffHideIds } from "../payPush/utils";
import { diffDataHandle } from "./utils";

interface Options {
  userId: string;
  getDiffPriceDataApi: (params: any) => Promise<any>;
  getDiffSettingApi: () => Promise<any>;
}

export const useNewDiffPrice = (options: Options) => {
  const { userId, getDiffPriceDataApi, getDiffSettingApi } = options;

  const loading = ref(false);
  const dataList = ref<any[]>([]);
  const cloneData = ref([]);
  const searchForm = ref({
    bigVipType: "",
    goodName: undefined,
    amount: undefined
  });

  const getData = () => {
    loading.value = true;
    getDiffPriceDataApi({ userid: userId })
      .then(async res => {
        const diffSettingData = await getDiffSettingApi();
        const hideIds = getDiffHideIds(diffSettingData);
        const data = res.data.list.filter(item => {
          item.cloneName = item.skuGoodName;

          item.showQR = diffSettingData.data.infos.find(
            info => info.skuGoodId === item.skuGoodId
          )?.showQR;

          diffDataHandle(item);

          return item.canJoin && !hideIds.includes(item.skuGoodId);
        });

        dataList.value = data;

        cloneData.value = data;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const resetData = () => {
    searchForm.value = {
      bigVipType: "",
      goodName: undefined,
      amount: undefined
    };
    dataList.value = cloneData.value;
  };

  const searchData = () => {
    const { bigVipType, goodName, amount } = searchForm.value;
    const data = cloneDeep(cloneData.value);
    const filterData = data.filter(item => {
      if (goodName) {
        item.skuGoodName = item.skuGoodName.replace(
          new RegExp(goodName, "g"),
          `<span style="color: red">${goodName}</span>`
        );
      }
      if (bigVipType && item.stage !== bigVipType) {
        return false;
      }

      if (goodName && !item.skuGoodName.includes(goodName)) {
        return false;
      }
      if (amount && item.amount !== amount) {
        return false;
      }
      return true;
    });
    dataList.value = filterData;
  };

  // 获取优惠截止时间
  const getDemarcationTime = () => {
    let time = undefined;
    cloneData.value.forEach(item => {
      item.deductibleOrders?.forEach(order => {
        if (order.endTime) {
          if (
            !time ||
            new Date(order.endTime).getDate() < new Date(time).getTime()
          ) {
            time = order.endTime;
          }
        }
      });
    });
    return time ? dayjs(time).format("YYYY-MM-DD HH:mm:ss") : undefined;
  };

  return {
    loading,
    dataList,
    cloneData,
    searchForm,
    getData,
    resetData,
    searchData,
    getDemarcationTime
  };
};
