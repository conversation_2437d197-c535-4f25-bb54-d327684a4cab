<!--
 * @Date         : 2025-06-26 16:05:10
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { getProfile<PERSON>pi, setProfile<PERSON>pi } from "/@/api/active";

const loading = ref(false);
const formRef = ref<FormInstance>();

const form = ref({
  time: []
});

const onSubmit = async () => {
  loading.value = true;
  const data = {
    start_time: form.value.time?.[0] || "",
    end_time: form.value.time?.[1] || ""
  };
  setProfileApi({
    name: "allocation_expire_setting",
    value: data,
    note: "服务期冷静期"
  })
    .then(() => {
      ElMessage.success("操作成功");
      getData();
    })
    .finally(() => {
      loading.value = false;
    });
};

const getData = () => {
  loading.value = true;
  getProfileApi({ name: "allocation_expire_setting" })
    .then(({ data }: { data: any }) => {
      if (data.value.start_time) {
        form.value.time = [data.value.start_time, data.value.end_time];
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 初始化数据
getData();
</script>

<template>
  <div v-loading="loading">
    <div class="g-wrapper-no">服务期冷静期</div>
    <el-row :gutter="20">
      <el-col :span="4" :offset="0" />
      <el-col :span="16" :offset="0">
        <el-form ref="formRef" :model="form">
          <el-form-item>
            <div class="mr-4px">
              <SyncDatePicker
                v-model:value="form.time"
                type="datetimerange"
                value-format="YYYY-MM-DD HH:mm:ss"
                range-separator="至"
                start-placeholder="线索到期时间-开始"
                end-placeholder="线索到期时间-结束"
                :default-time="[
                  new Date(2000, 1, 1, 0, 0, 0),
                  new Date(2000, 2, 1, 23, 59, 59)
                ]"
              />
            </div>
            内服务期冷静期规则不启用
          </el-form-item>

          <div>
            <el-form-item>
              <el-button type="primary" @click="onSubmit" :loading="loading">
                保存
              </el-button>
            </el-form-item>
          </div>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
.input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.input-group .el-input {
  flex: 1;
}

.ml-2 {
  margin-left: 8px;
}

.text-gray-500 {
  color: #6b7280;
  font-size: 14px;
}
</style>
