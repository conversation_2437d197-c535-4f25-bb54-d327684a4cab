<script lang="ts" setup>
import { videoHistoryApi } from "@/api/customer/details";
import { useSearch } from "@/hooks/useSearch";
import { timeChange } from "@/utils/common";
import MyList from "@/components/MyList/index.vue";

const props = defineProps<{
  userId: string;
}>();

const { list, loading, finished, error, onLoad } = useSearch({
  api: videoHistoryApi,
  initParams: {
    userId: props.userId
  }
});
</script>

<template>
  <div class="h-100% p-20px">
    <MyList
      v-model:error="error"
      :list="list"
      :loading="loading"
      :finished="finished"
      :onLoad="onLoad"
    >
      <template #default="{ data }">
        <div class=" mb-20px">
          <van-cell-group :border="false">
            <van-cell :border="false" title="学期" :value="data.semesterName" />
            <van-cell :border="false" title="学科" :value="data.subjectName" />
            <van-cell
              :border="false"
              title="教材"
              :value="data.publisherName"
            />
            <van-cell
              :border="false"
              title="视频名称"
              :value="data.video.name"
            />
            <van-cell
              :border="false"
              title="知识点名称"
              :value="data.topic.name"
            />
            <van-cell
              :border="false"
              title="是否付费内容"
              :value="
                data.topic.isFreeTime
                  ? '限免'
                  : data.topic.pay
                  ? '付费'
                  : '免费'
              "
            />
            <van-cell
              :border="false"
              title="观看时间"
              :value="timeChange(data.timestamp, 2)"
            />
          </van-cell-group>
        </div>
      </template>
    </MyList>
  </div>
</template>

<style lang="scss" scoped></style>
