import { defineStore } from 'pinia'
import api from '@/api'
import dayjs from 'dayjs'
import router from '@/router'
import { useCall } from './call'
import { getVersion } from '@/script/version'
import { useConfig } from './config'

interface UserState {
  token: string

  name: string
  phone: string
  email: string
  username: string
  qmExten: string // 外呼工号
  channel: string // 外呼渠道
  domain: string
  sipHost: string
  password: string
  channelId: number
  workerId: number
  permissions: string[] // 权限列表

  avatar: string

  version: string
}

export const useUser = defineStore({
  id: 'USER',
  state(): UserState {
    return {
      token: '',

      name: '',
      phone: '',
      email: '',
      username: '',
      qmExten: '',
      channel: '',
      domain: '',
      sipHost: '',
      password: '',
      channelId: 0,
      workerId: 0,
      permissions: [],

      avatar: '',

      version: ''
    }
  },
  getters: {
    isasr(state:UserState): boolean {
      return (this.permissions.includes('telesale_admin_custom_detailsV2') &&
              this.permissions.includes('telesale_show_ai_call') &&
              (this.permissions.includes('telesale_show_ai_sop') ||
              this.permissions.includes('telesale_show_ai_tips') ||
              this.permissions.includes('telesale_show_ai_keyword') ||
              this.permissions.includes('telesale_show_ai_talk')))
    },
    passwordFormat(state: UserState): string {
      return this.password.replace('{agent_no}', this.qmExten)
    }
  },
  actions: {
    async login(par: {
      mail: string
      code: string
    }) {
      const res = await api.login(par)
      this.token = res.token

      ElMessage.success('登录成功')
      router.push('/Home')
    },
    logout() {
      this.token = ''
      router.push('/Login')
    },
    async getVersion() {
      try {
        // const version = await api.getVersion()
        const version = await getVersion()
        console.log('App版本号：', version)
        this.version = version
      } catch (error) {
        console.log('error', error)
      }
    },
    async getUserInfo() {
      const res = await api.getUserInfo()

      this.name = res.name
      this.phone = res.phone
      this.email = res.email
      this.username = res.username
      this.qmExten = res.qmExten
      this.channel = res.channel
      this.channelId = res.channelId
      this.workerId = res.workerId
      this.permissions = res.permissions
      this.domain = res.domain
      this.sipHost = res.sipHost
      this.password = res.password

      // 获取配置
      useConfig().getConfig()

      if (res.avatar) {
        api.getPic(res.avatar).then((data) => {
          this.avatar = data
        })
      }
    },
    uploadLog() {
      let target:any = {
        workerId: this.workerId,
        currentDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        appVersion: this.version,
        channel: this.channel
      }

      if (useCall().phone) target.phone = useCall().phone

      for (const key of Object.keys(target)) {
        target[key] = JSON.stringify(target[key])
      }

      api.uploadLog({
        data: target
      })
    }
  },
  persist: {
    paths: ['token']
  }
})
