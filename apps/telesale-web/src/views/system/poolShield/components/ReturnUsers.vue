<!--
 * @Date         : 2025-06-19 17:15:29
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { ref, computed, nextTick } from "vue";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import { getProfileApi, setProfileApi } from "/@/api/active";
import { cloneDeep } from "lodash-es";

const loading = ref(false);
const formRef = ref<FormInstance>();

const form = ref({
  state: undefined,
  day: undefined,
  frequency: undefined
});

// 动态表单验证规则
const rules = computed<FormRules>(() => {
  const isEnabled = form.value.state === 2;

  return {
    day: [
      {
        required: isEnabled,
        message: "请输入天数",
        trigger: "blur"
      },
      {
        type: "number",
        min: 1,
        max: 365,
        message: "天数必须在1-365之间",
        trigger: "blur"
      }
    ],
    frequency: [
      {
        required: isEnabled,
        message: "请输入回流次数",
        trigger: "blur"
      },
      {
        type: "number",
        min: 1,
        message: "回流次数必须大于0",
        trigger: "blur"
      }
    ]
  };
});

const onSubmit = async () => {
  // 如果开关开启，需要验证表单
  if (form.value.state === 2) {
    try {
      await formRef.value.validate();
    } catch (error) {
      ElMessage.warning("请完善表单信息");
      return;
    }
  }

  loading.value = true;
  const data = cloneDeep(form.value);
  setProfileApi({
    name: "customers_returning",
    value: data,
    note: "短期内多次回流的客户"
  })
    .then(() => {
      ElMessage.success("操作成功");
      getData();
    })
    .finally(() => {
      loading.value = false;
    });
};

const getData = () => {
  loading.value = true;
  getProfileApi({ name: "customers_returning" })
    .then(({ data }: { data: any }) => {
      form.value = data.value;
    })
    .finally(() => {
      loading.value = false;
    });
};
getData();
</script>

<template>
  <div v-loading="loading">
    <div class="g-wrapper-no">短期内多次回流的客户</div>
    <el-row :gutter="20">
      <el-col :span="4" :offset="0" />
      <el-col :span="16" :offset="0">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          inline
          label-width="auto"
        >
          <div>
            <el-form-item label="是否开启">
              <el-switch
                v-model="form.state"
                :active-value="2"
                :inactive-value="1"
              />
            </el-form-item>
          </div>
          <el-form-item prop="day">
            <el-input-number
              v-model="form.day"
              :min="1"
              :max="365"
              :precision="0"
              placeholder="请输入天数"
            />
            <span class="mx-4px">天内回流</span>
          </el-form-item>
          <el-form-item prop="frequency">
            <el-input-number
              v-model="form.frequency"
              :min="1"
              :max="99999999"
              :precision="0"
              placeholder="请输入次数"
            />
            <span class="ml-4px">次的线索不允许进入公海池</span>
          </el-form-item>
          <div>
            <el-form-item>
              <el-button type="primary" @click="onSubmit" :loading="loading">
                保存
              </el-button>
            </el-form-item>
          </div>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped></style>
