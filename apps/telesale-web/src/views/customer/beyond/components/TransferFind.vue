<script setup lang="ts" name="TransferFind">
import { ref, reactive, getCurrentInstance } from "vue";
import timeChange from "/@/utils/handle/timeChange";
import { findReferral } from "/@/api/customer";
import { order } from "/@/api/customerDetails";

const global = getCurrentInstance().appContext.config.globalProperties;

const loading = ref<boolean>(false);
const form = reactive({
  phone: "",
  type: 2
});

interface InfoType {
  paidTime: string;
  originalAmount: string | number;
  onionId: string | number;
  status: string;
  name: string;
  grade: string;
}

const info = reactive<InfoType>({
  paidTime: "",
  originalAmount: "",
  onionId: "",
  status: "",
  name: "",
  grade: ""
});

const onSearch = () => {
  if (form.type === 1 && !/^\d{8,11}$/.test(form.phone)) {
    global.$message.warning("洋葱ID格式有误");
    return;
  }
  if (form.type === 2 && !/^\d{11}$/.test(form.phone)) {
    global.$message.warning("手机号格式有误");
    return;
  }
  loading.value = true;
  findReferral({ [form.type === 1 ? "onionId" : "phone"]: form.phone })
    .then(({ data }: { data: any }) => {
      if (data.userId) {
        info.onionId = data.onionId;
        info.grade = data.grade;
        getList(data.userId);
      } else {
        info.onionId = "";
        info.grade = "";
        initData();
        loading.value = false;
      }
    })
    .catch(() => {
      info.onionId = "";
      info.grade = "";
      initData();
      loading.value = false;
    });
};

function initData() {
  let obj = {
    paidTime: "",
    originalAmount: "",
    status: "",
    name: ""
  };
  for (let key in obj) {
    form[key] = "";
  }
}
function getList(userId) {
  loading.value = true;
  order({ userid: userId })
    .then(({ data }: { data: any }) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i].status === "支付成功" || data[i].status === "退款成功") {
          info.name = data[i].good.name;
          info.paidTime = data[i].paidTime;
          info.originalAmount = data[i].good.amount;
          info.status = data[i].status;
          loading.value = false;
          break;
        }
      }
      initData();
      loading.value = false;
    })
    .catch(() => {
      initData();
      loading.value = false;
    });
}
</script>

<template>
  <div class="d_container">
    <div class="d_box" v-loading="loading">
      <el-form class="d-form" :inline="true" :model="form" @submit.prevent>
        <el-form-item prop="phone">
          <el-select v-model="form.type">
            <el-option label="洋葱ID" :value="1" />
            <el-option label="手机号" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="form.phone"
            :placeholder="
              form.type === 1 ? '请输入客户洋葱ID' : '请输入客户手机号'
            "
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
      <div class="text" v-if="info.onionId">
        <div>推荐人洋葱ID：{{ info.onionId }}</div>
        <div>推荐人年级：{{ info.grade }}</div>
        <div>商品名称：{{ info.name }}</div>
        <div>订单总价：{{ info.originalAmount }}</div>
        <div>支付成功时间：{{ timeChange(info.paidTime, 3) }}</div>
        <div>订单状态：{{ info.status }}</div>
      </div>
      <div class="text d-no-data" v-else>暂无转介绍数据</div>
    </div>
  </div>
</template>
<style scoped lang="scss">
:deep(.el-select .el-input) {
  width: 100px;
}
.d_container {
  padding: 10vh 10px 15vh;
}
.d_box {
  margin: 0 auto;
  max-width: 500px;
  height: 320px;
  border-radius: 16px;
  background-color: #3d3e49;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.7), 0 0 20px rgba(0, 0, 0, 0.3) inset;
  position: relative;
  padding: 20px;
  text-align: center;

  .d-form {
    padding-top: 10px;
    border-bottom: 1px dashed #ccc;
    margin-bottom: 20px;
  }

  .text {
    color: #fff;
    font-size: 18px;
    &.d-no-data {
      margin-top: 60px;
    }

    > div {
      text-align: left;
      width: 320px;
      margin: 0 auto 8px;
      word-break: break-all;
    }
  }
}
</style>
