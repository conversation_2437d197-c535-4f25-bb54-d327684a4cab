import { childrenType } from "../../layout/types";
import { useUserStoreHook } from "/@/store/modules/user";
import { useAppStore } from "/@/store/modules/app";
import { storeToRefs } from "pinia";

const { device } = storeToRefs(useAppStore());

const menuAllData: childrenType[] = [
  {
    path: "/",
    value: "",
    permission: "",
    meta: {
      icon: "home-filled",
      title: "首页"
    },
    children: [
      {
        path: "/index",
        value: "",
        permission: "",
        meta: {
          title: "首页"
        }
      }
    ]
  },
  {
    path: "/dataPool",
    value: "",
    permission: "all",
    meta: {
      icon: "data-board",
      title: "公海池"
    },
    children: [
      {
        path: "/dataPool/index",
        value: "",
        permission: "telesale_admin_pool_find",
        meta: {
          title: "公海池"
        }
      }
    ]
  },
  {
    path: "/customer",
    value: "",
    permission: "all",
    meta: {
      icon: "avatar",
      title: "客户管理"
    },
    children: [
      {
        path: "/customer/ongoing/index",
        value: "",
        permission: "telesale_admin_custom_all_list",
        meta: {
          title: "客户池"
        }
      },
      {
        path: "/customer/done/index",
        value: "",
        permission: "telesale_admin_custom_all_list",
        meta: {
          title: "已成交"
        }
      },
      {
        path: "/customer/payClue/index",
        value: "",
        permission: "telesale_admin_custom_pay_clue",
        meta: {
          title: "付费线索池"
        }
      },
      {
        path: "/customer/referralClue/index",
        value: "",
        permission: "telesale_admin_custom_treferral_clue",
        meta: {
          title: "转介绍跟进"
        }
      },
      {
        path: "/customer/verifyBindingFamily/index",
        value: "",
        permission: "telesale_admin_custom_verifyBindingFamily",
        meta: {
          title: "核实绑定家庭"
        }
      },
      {
        path: "/customer/followClue/index",
        value: "",
        permission: "telesale_admin_custom_follow_clue",
        meta: {
          title: "推送优质线索池"
        }
      },
      {
        path: "/customer/collect/index",
        value: "",
        permission: "telesale_admin_custom_collect_clue",
        meta: {
          title: "已收藏"
        }
      },
      {
        path: "/customer/push/index",
        value: "",
        permission: "telesale_admin_push_custom_record",
        meta: {
          title: "推送线索记录"
        }
      },
      // {
      //   path: "/customer/aladdinBeyond/index",
      //   value: "",
      //   permission: "telesale_admin_custom_beyond_aladdinMenu",
      //   meta: {
      //     title: "阿拉丁线索查询"
      //   }
      // },
      {
        path: "/customer/beyond/index",
        value: "",
        permission: "telesale_admin_custom_beyond",
        meta: {
          title: "线索查询"
        }
      },
      {
        path: "/customer/clueTrace/index",
        value: "",
        permission: "telesale_admin_custom_clueTrace",
        meta: {
          title: "线索溯源"
        }
      },
      {
        path: "/customer/newDiff/index",
        value: "",
        permission: "telesale_admin_diff_price",
        meta: {
          title: "差价查询（新）"
        }
      },
      {
        path: "/customer/exclusiveLink/index",
        value: "",
        permission: "telesale_admin_new_exclusiveLink",
        meta: {
          title: "专属链接（新）"
        }
      },
      {
        path: "/customer/link/index",
        value: "",
        permission: "telesale_admin_exclusiveLink",
        meta: {
          title: "专属链接"
        }
      },
      {
        path: "/customer/linkSetting/index",
        value: "",
        permission: "telesale_admin_exclusiveLink_setting",
        meta: {
          title: "会场/补差链接配置"
        }
      },
      {
        path: "/customer/comWeActive/index",
        value: "",
        permission: "telesale_admin_comWeActive",
        meta: {
          title: "企微好友活跃度排行"
        }
      }
    ]
  },
  {
    path: "/order",
    value: "",
    permission: "all",
    meta: {
      icon: "checked",
      title: "订单管理"
    },
    children: [
      {
        path: "/order/order/index",
        value: "",
        permission: "telesale_admin_order_all_list",
        meta: {
          title: "订单列表"
        }
      },
      {
        path: "/order/appeal/index",
        value: "",
        permission: "telesale_admin_appeal_all_list",
        meta: {
          title: "申诉列表"
        }
      },
      {
        path: "/order/appealOrder/index",
        value: "",
        permission: "telesale_admin_appeal_order_list",
        meta: {
          title: "申诉订单"
        }
      }
    ]
  },
  {
    path: "/statistics",
    value: "",
    permission: "all",
    meta: {
      icon: "histogram",
      title: "数据统计"
    },
    children: [
      {
        path: "/statistics/rankPerson/index",
        value: "",
        permission: "telesale_admin_rank_person",
        meta: {
          title: "个人排行榜"
        }
      },
      {
        path: "/statistics/rankGroup/index",
        value: "",
        permission: "telesale_admin_rank_group",
        meta: {
          title: "小组排行榜"
        }
      },
      {
        path: "/statistics/rankTeam/index",
        value: "",
        permission: "telesale_admin_rank_team",
        meta: {
          title: "团队排行榜"
        }
      },
      {
        path: "/statistics/revenue/index",
        value: "",
        permission: "telesale_admin_revenue",
        meta: {
          title: "营收"
        }
      },
      {
        path: "/statistics/salary/index",
        value: "",
        permission: "telesale_admin_salary_all_list",
        meta: {
          title: "绩效"
        }
      },
      {
        path: "/statistics/matchConversion/index",
        value: "",
        permission: "telesale_admin_matchConversion",
        meta: {
          title: "当配转化统计"
        }
      },
      {
        path: "/statistics/matchOutbound/index",
        value: "",
        permission: "telesale_admin_matchOutbound",
        meta: {
          title: "当配外呼统计"
        }
      },
      {
        path: "/statistics/cluePushService/index",
        value: "",
        permission: "telesale_admin_servicePushStatistic",
        meta: {
          title: "客服推送统计"
        }
      },
      {
        path: "/statistics/cluePushAgent/index",
        value: "",
        permission: "telesale_admin_agentReceptionStatistic",
        meta: {
          title: "坐席接收统计"
        }
      },
      {
        path: "/statistics/rescueOrder/index",
        value: "",
        permission: "telesale_admin_statistics_rescueOrder",
        meta: {
          title: "挽单成功率统计"
        }
      },
      {
        path: "/statistics/transferRevenue/index",
        value: "",
        permission: "telesale_admin_statistics_transfer",
        meta: {
          title: "转介绍业绩统计"
        }
      },
      {
        path: "/statistics/rankTransfer/index",
        value: "",
        permission: "telesale_admin_ranking",
        meta: {
          title: "转介绍排行榜"
        }
      }
    ]
  },
  {
    path: "/active",
    value: "",
    permission: "all",
    meta: {
      icon: "flag",
      title: "活动管理"
    },
    children: [
      {
        path: "/active/transfer/index",
        value: "",
        permission: "telesale_admin_transferActive",
        meta: {
          title: "转介绍"
        }
      },
      {
        path: "/active/channel/index",
        value: "",
        permission: "telesale_admin_channelCode_list",
        meta: {
          title: "渠道活码"
        }
      },
      {
        path: "/active/channelType/index",
        value: "",
        permission: "telesale_admin_channelType_list",
        meta: {
          title: "活码类型管理"
        }
      },
      {
        path: "/active/ruleConfig/index",
        value: "",
        permission: "telesale_admin_ruleConfig_list",
        meta: {
          title: "活码分配规则"
        }
      },
      {
        path: "/active/uiConfig/index",
        value: "",
        permission: "telesale_admin_uiConfig_list",
        meta: {
          title: "活码配置"
        }
      },
      {
        path: "/active/fission/index",
        value: "",
        permission: "telesale_admin_fission_active",
        meta: {
          title: "裂变活动",
          showParent: true
        },
        children: [
          {
            path: "/active/fission/fissionUserDetail/index",
            value: "",
            permission: "telesale_admin_fission_active_fissionUserDetail",
            meta: {
              title: "用户参与明细"
            }
          },
          {
            path: "/active/fission/prizeDetail/index",
            value: "",
            permission: "telesale_admin_fission_active_prizeDetail",
            meta: {
              title: "活动中奖明细"
            }
          }
        ]
      },
      {
        path: "/active/welcomeMessage/index",
        value: "",
        permission: "telesale_admin_WelcomeMessage_list",
        meta: {
          title: "分时段欢迎语"
        }
      },
      {
        path: "/active/themeConfig/index",
        value: "",
        permission: "telesale_admin_active_themeConfig",
        meta: {
          title: "引流页主题配置"
        }
      },
      {
        path: "/active/videoNumber/index",
        value: "",
        permission: "telesale_admin_videoNumber",
        meta: {
          title: "视频号素材"
        }
      }
    ]
  },
  {
    path: "/integralMall",
    value: "",
    permission: "all",
    meta: {
      icon: "shop",
      title: "积分商城"
    },
    children: [
      {
        path: "/integralMall/goodsList/index",
        value: "",
        permission: "telesale_admin_mall_goods",
        meta: {
          title: "商品管理"
        }
      },
      {
        path: "/integralMall/jdCard/index",
        value: "",
        permission: "telesale_admin_mall_jdCard",
        meta: {
          title: "京东卡管理"
        }
      },
      {
        path: "/integralMall/goodsOrderList/index",
        value: "",
        permission: "telesale_admin_mall_order",
        meta: {
          title: "订单管理"
        }
      },
      {
        path: "/integralMall/freightSetting/index",
        value: "",
        permission: "telesale_admin_mall_freightSetting",
        meta: {
          title: "运费设置"
        }
      },
      {
        path: "/integralMall/integralList/index",
        value: "",
        permission: "telesale_admin_mall_integral",
        meta: {
          title: "积分管理"
        }
      },
      {
        path: "/integralMall/pictureReview/index",
        value: "",
        permission: "telesale_admin_mall_review",
        meta: {
          title: "图片审核"
        }
      },
      {
        path: "/integralMall/pointRuleSetting/index",
        value: "",
        permission: "telesale_admin_mall_pointRuleSetting",
        meta: {
          title: "积分规则设置"
        }
      }
    ]
  },
  {
    path: "/salarySystem",
    value: "",
    permission: "all",
    meta: {
      icon: "credit-card",
      title: "薪酬体系"
    },
    children: [
      {
        path: "/salarySystem/agentSalary/index",
        value: "",
        permission: "telesale_admin_agentSalary",
        meta: {
          title: "坐席查询"
        }
      },
      {
        path: "/salarySystem/actualAttendance/index",
        value: "",
        permission: "telesale_admin_actualAttendance",
        meta: {
          title: "实际出勤"
        }
      },
      {
        path: "/salarySystem/lookTarget/index",
        value: "",
        permission: "telesale_admin_lookTarget",
        meta: {
          title: "查看目标"
        }
      },
      {
        path: "/salarySystem/setTeamTarget/index",
        value: "",
        permission: "telesale_admin_target",
        meta: {
          title: "团队目标"
        }
      }
    ]
  },
  {
    path: "/system",
    value: "",
    permission: "all",
    meta: {
      icon: "setting",
      title: "系统设置"
    },
    children: [
      {
        path: "/system/distribute/index",
        value: "",
        permission: "telesale_admin_system_distribute",
        meta: {
          title: "线索分配设置"
        }
      },
      {
        path: "/system/mvpSet/index",
        value: "",
        permission: "telesale_admin_mvpSet",
        meta: {
          title: "私域分配设置"
        }
      },
      {
        path: "/system/orientation/index",
        value: "",
        permission: "telesale_admin_orientation",
        meta: {
          title: "线索定向分配"
        }
      },
      {
        path: "/system/cueModel/index",
        value: "",
        permission: "telesale_admin_system_cueModel",
        meta: {
          title: "线索模型设置"
        }
      },
      {
        path: "/system/shield/index",
        value: "",
        permission: "telesale_admin_system_shield",
        meta: {
          title: "屏蔽线索设置"
        }
      },
      {
        path: "/system/poolShield/index",
        value: "",
        permission: "telesale_admin_system_poolShield",
        meta: {
          title: "公海池屏蔽规则设置"
        }
      },
      {
        path: "/system/selectAgent/index",
        value: "",
        permission: "telesale_admin_system_selectAgent",
        meta: {
          title: "坐席选择设置"
        }
      },
      {
        path: "/system/qualityTesting/index",
        value: "",
        permission: "telesale_admin_qualityTesting",
        meta: {
          title: "质检坐席设置"
        }
      },
      {
        path: "/system/qualityCheckDurationSet/index",
        value: "",
        permission: "telesale_admin_system_qualityCheckDurationSet",
        meta: {
          title: "质检时长设置"
        }
      },
      {
        path: "/system/feishuPush/index",
        value: "",
        permission: "telesale_admin_system_feishuPush",
        meta: {
          title: "飞书推送设置"
        }
      },
      {
        path: "/system/abTest/index",
        value: "",
        permission: "telesale_admin_system_abTest",
        meta: {
          title: "AB测比例设置"
        }
      },
      {
        path: "/system/tag/index",
        value: "",
        permission: "telesale_admin_system_tag",
        meta: {
          title: "线索标签设置"
        }
      },
      {
        path: "/system/cardTemplate/index",
        value: "",
        permission: "telesale_admin_system_cardTemplate",
        meta: {
          title: "卡片模板设置"
        }
      },
      {
        path: "/system/explosiveMonogroup/index",
        value: "",
        permission: "telesale_admin_system_explosiveMonogroup",
        meta: {
          title: "爆单群设置"
        }
      },
      {
        path: "/system/salarySet/index",
        value: "",
        permission: "telesale_admin_system_salarySet",
        meta: {
          title: "坐席薪酬设置"
        }
      },
      {
        path: "/system/giftTimeSet/index",
        value: "",
        permission: "telesale_admin_system_giftTimeSet",
        meta: {
          title: "活动时间设置"
        }
      },
      {
        path: "/system/callInterval/index",
        value: "",
        permission: "telesale_admin_system_callInterval",
        meta: {
          title: "外呼间隔时长设置"
        }
      },
      {
        path: "/system/smsTemplate/index",
        value: "",
        permission: "telesale_admin_system_smsTemplate",
        meta: {
          title: "短信模板设置"
        }
      },
      {
        path: "/system/cueSetting/index",
        value: "",
        permission: "telesale_admin_system_cueSetting",
        meta: {
          title: "线索操作设置"
        }
      },
      {
        path: "/system/callWarning/index",
        value: "",
        permission: "telesale_admin_system_callWarning",
        meta: {
          title: "外呼行为预警提醒"
        }
      }
    ]
  },
  {
    path: "/agent",
    value: "",
    permission: "all",
    meta: {
      icon: "stamp",
      title: "坐席管理"
    },
    children: [
      {
        path: "/agent/organizational/index",
        value: "",
        permission: "telesale_admin_organization",
        meta: {
          title: "组织架构"
        }
      },
      {
        path: "/agent/agent/index",
        value: "",
        permission: "telesale_admin_worker_list",
        meta: {
          title: "坐席列表"
        }
      }
    ]
  },
  {
    path: "/daily",
    value: "",
    permission: "all",
    meta: {
      icon: "collection",
      title: "日常运营"
    },
    children: [
      {
        path: "/daily/callRecord/index",
        value: "",
        permission: "telesale_admin_call_all_list",
        meta: {
          title: "呼叫记录"
        }
      },
      {
        path: "/daily/excellentRecording/index",
        value: "",
        permission: "telesale_admin_excellent_recording",
        meta: {
          title: "优秀录音"
        }
      },
      {
        path: "/daily/updateNotice/index",
        value: "",
        permission: "telesale_admin_notice",
        meta: {
          title: "版本公告"
        }
      },
      {
        path: "/daily/scheduling/index",
        value: "",
        permission: "telesale_admin_scheduling_list",
        meta: {
          title: "排班"
        }
      },
      {
        path: "/daily/qrcodeAttendance/index",
        value: "",
        permission: "telesale_admin_qrcodeAttendance",
        meta: {
          title: "活码坐席设置"
        }
      },
      {
        path: "/daily/numberPool/index",
        value: "",
        permission: "telesale_admin_daily_numberPool",
        meta: {
          title: "号码池"
        }
      },
      {
        path: "/daily/cancel/index",
        value: "",
        permission: "telesale_admin_cancel_account",
        meta: {
          title: "注销列表"
        }
      },
      {
        path: "/daily/release/index",
        value: "",
        permission: "telesale_admin_release_record",
        meta: {
          title: "释放记录"
        }
      },
      {
        path: "/daily/jobNOPool/index",
        value: "",
        permission: "telesale_admin_agent_number",
        meta: {
          title: "工号池"
        }
      },
      {
        path: "/daily/weChatWork/index",
        value: "",
        permission: "telesale_admin_weChatWork",
        meta: {
          title: "企微账号管理"
        }
      },
      {
        path: "/daily/miniprogramSet/index",
        value: "",
        permission: "telesale_admin_miniprogram",
        meta: {
          title: "小程序设置"
        }
      },
      {
        path: "/daily/callWays/index",
        value: "",
        permission: "telesale_admin_daily_callWays",
        meta: {
          title: "外呼渠道"
        }
      },
      {
        path: "/daily/callTeam/index",
        value: "",
        permission: "telesale_admin_daily_callTeam",
        meta: {
          title: "外呼团队管理"
        }
      },
      {
        path: "/daily/profile/index",
        value: "",
        permission: "telesale_admin_daily_profile",
        meta: {
          title: "专属资料管理"
        }
      },
      {
        path: "/daily/linkPoster/index",
        value: "",
        permission: "telesale_admin_daily_linkPoster",
        meta: {
          title: "课程链接海报"
        }
      },
      {
        path: "/daily/interface/index",
        value: "",
        permission: "telesale_admin_daily_interface",
        meta: {
          title: "接口管理"
        }
      },
      {
        path: "/daily/operateLog/index",
        value: "",
        permission: "telesale_admin_operateLog",
        meta: {
          title: "操作日志"
        }
      }
    ]
  },
  {
    path: "/warm",
    value: "",
    permission: "all",
    meta: {
      icon: "stamp",
      title: "暖暖加油站"
    },
    children: [
      {
        path: "/warm/material/index",
        value: "",
        permission: "telesale_admin_warm_material",
        meta: {
          title: "暖暖素材库"
        }
      },
      {
        path: "/warm/comment/index",
        value: "",
        permission: "telesale_admin_warm_comment",
        meta: {
          title: "暖暖评论统计"
        }
      },
      {
        path: "/warm/channel/index",
        value: "",
        permission: "telesale_admin_warm_channel",
        meta: {
          title: "暖暖渠道管理"
        }
      },
      {
        path: "/warm/scene/index",
        value: "",
        permission: "telesale_admin_warm_scene",
        meta: {
          title: "暖暖场景管理"
        }
      },
      {
        path: "/warm/h5Manage/index",
        value: "",
        permission: "telesale_admin_warm_h5Manage",
        meta: {
          title: "暖暖H5页面管理"
        }
      }
    ]
  },
  {
    path: "/setting",
    value: "",
    permission: "all",
    meta: {
      icon: "set-up",
      title: "个人设置",
      showParent: true
    },
    children: [
      {
        path: "/setting/larnWarn/index",
        value: "",
        permission: "telesale_admin_setting_larnWarn",
        meta: {
          title: "飞书提醒设置"
        }
      }
    ]
  },
  {
    path: "/salesman",
    value: "",
    permission: "all",
    meta: {
      icon: "set-up",
      title: "销售人员管理",
      showParent: true,
      hideMobile: true
    },
    children: [
      {
        path: "/salesman/userList",
        value: "",
        permission: "telesale_admin_salesman_userList",
        meta: {
          title: "人员列表"
        }
      }
      // {
      //   path: "/salesman/userDetail",
      //   value: "",
      //   permission: "telesale_admin_salesman_userList",
      //   meta: {
      //     title: "档案详情"
      //   }
      // }
    ]
  },
  // {
  //   path: "/aladdin",
  //   value: "",
  //   permission: "all",
  //   meta: {
  //     icon: "set-up",
  //     title: "阿拉丁转介绍",
  //     showParent: true,
  //     hideMobile: true
  //   },
  //   children: [
  //     {
  //       path: "/aladdin/referral/index",
  //       value: "",
  //       permission: "telesale_admin_aladdin_referral",
  //       meta: {
  //         title: "转介绍列表"
  //       }
  //     },
  //     {
  //       path: "/aladdin/posterTheme/index",
  //       value: "",
  //       permission: "telesale_admin_aladdin_posterTheme",
  //       meta: {
  //         title: "海报主题"
  //       }
  //     }
  //   ]
  // },
  {
    path: "/AISupport",
    value: "",
    permission: "all",
    meta: {
      icon: "set-up",
      title: "AI辅助",
      showParent: true,
      hideMobile: true
    },
    children: [
      {
        path: "/AISupport/Quality",
        value: "",
        permission: "telesale_admin_AISupport_quality",
        meta: {
          title: "AI质检"
        }
      },
      {
        path: "/AISupport/SopConfig",
        value: "",
        permission: "telesale_admin_AISupport_SopConfig",
        meta: {
          title: "SOP管理"
        }
      },
      {
        path: "/AISupport/IssuesConfig",
        value: "",
        permission: "telesale_admin_AISupport_IssuesConfig",
        meta: {
          title: "议题模板管理"
        }
      },
      {
        path: "/AISupport/ScoreLine",
        value: "",
        permission: "telesale_admin_AISupport_ScoreLine",
        meta: {
          title: "分数线管理"
        }
      }
    ]
  },
  {
    path: "/aiQualityInspection",
    value: "",
    permission: "all", // 根据实际需要调整权限
    meta: {
      icon: "set-up", // 选择一个合适的图标
      title: "AI陪练台"
    },
    children: [
      {
        path: "/aiQualityInspection/excellentCases/index",
        value: "",
        permission: "telesale_admin_ai_excellentCases",
        meta: {
          title: "优秀案例库"
        }
      },
      {
        path: "/aiQualityInspection/excellentQA/index",
        value: "",
        permission: "telesale_admin_ai_excellentQA",
        meta: {
          title: "优秀问答库"
        }
      },
      {
        path: "/aiQualityInspection/courseManagement/index",
        value: "",
        permission: "telesale_admin_ai_courseManagement",
        meta: {
          title: "课程管理"
        }
      },
      {
        path: "/aiQualityInspection/taskManagement/index",
        value: "",
        permission: "telesale_admin_ai_taskManagement",
        meta: {
          title: "任务管理"
        }
      },
      {
        path: "/aiQualityInspection/myTask/index",
        value: "",
        permission: "telesale_admin_ai_myTask",
        meta: {
          title: "我的任务"
        }
      }
    ]
  }
];

const isShow = (list: childrenType[]) => {
  const rtn: childrenType[] = [];
  list.forEach(item => {
    if (!item.permission || hasPermission(item.permission)) {
      rtn.push(item);
    }
    if (item.children?.length) {
      item.children = item.children?.filter(
        child => !child.permission || hasPermission(child.permission)
      );
    }
  });
  return rtn;
};
const hasPermission = key => {
  return useUserStoreHook().authorizationMap.indexOf(key) > -1;
};
const menuDataMath = () => {
  const menuData: childrenType[] = [];
  menuAllData.forEach(item => {
    if (!item.meta.hideMobile || device.value !== "mobile") {
      if (item.permission === "all") {
        const list = isShow(item.children);
        if (list.length > 0) {
          item.children = list;
          menuData.push(item);
        }
      } else {
        menuData.push(item);
      }
    }
  });

  import.meta.env.MODE !== "master" &&
    menuData.push({
      path: "/testUse",
      value: "",
      permission: "",
      meta: {
        icon: "opportunity",
        title: "测试工具"
      },
      children: [
        {
          path: "/testUse/index",
          value: "",
          permission: "",
          meta: {
            title: "模拟推送数据"
          }
        },
        {
          path: "/testUse/goods/index",
          value: "",
          permission: "",
          meta: {
            title: "自定义商品管理"
          }
        },
        {
          path: "/testUse/addOrder/index",
          value: "",
          permission: "",
          meta: {
            title: "创建订单"
          }
        }
      ]
    });

  useUserStoreHook().setMenuData(menuData);
};

export default menuDataMath;
