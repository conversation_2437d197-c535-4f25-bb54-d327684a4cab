import { toRefs, reactive, UnwrapRef, ref, Ref } from "vue";
import { showLoadingToast, closeToast } from "vant";

interface Options<T, P = Record<any, any>> {
  isPages?: boolean;
  immediate?: boolean;
  initParams?: P;
  api?: (params: P) => Promise<ReturnList<T>>;
  beforeRequest?: (params: UnwrapRef<P>) => boolean;
  requestError?: (err: any) => void;
  dataCallback?: (data: any) => void;
}

interface State<P = Record<any, any>> {
  loading: boolean;
  searchForm: P;
  error: boolean;
  errorText: string;
  pageInfo: Partial<PageInfo>;
  total: number;
}

interface PageInfo {
  pageIndex: number;
  pageSize: number;
}

export const useSearch = <T, P>(options: Options<T, P>) => {
  const {
    api,
    isPages = true,
    immediate = true,
    initParams = {},
    beforeRequest,
    requestError,
    dataCallback
  } = options;

  // 表格数据
  const list = ref<T[]>([]) as Ref<T[]>;
  const finished = ref<boolean>(false);
  const state = reactive<State<P>>({
    // 表格数据
    loading: false,
    // 错误状态
    error: false,
    // 错误文字
    errorText: "请求失败，点击重新加载",
    // 查询数据
    searchForm: {} as P,
    // 分页数据
    pageInfo: {
      pageIndex: 1,
      pageSize: 20
    },
    total: 0
  });

  const handlerQuery = async () => {
    if (!api) return console.info("[useTable] 没有请求api");
    // 请求之前该做的事情
    if (beforeRequest && !beforeRequest?.(state.searchForm)) return;
    state.loading = true;
    if (state.pageInfo.pageIndex === 1) {
      showLoadingToast("正在加载...");
    }
    try {
      const reParams = {
        ...initParams,
        ...(state.searchForm as P),
        ...(isPages ? state.pageInfo : {})
      };
      const res = await api(reParams);

      // 如果需要对数据进行处理
      dataCallback?.(res);
      list.value =
        state.pageInfo.pageIndex === 1
          ? res.list
          : [...list.value, ...res.list];
      state.total = res.total;
      state.loading = false;
    } catch (error) {
      console.error("[useTable requset error]", error);
      requestError?.(error);
      state.loading = false;
      state.error = true;
    } finally {
      finished.value = state.total === list.value.length;
      if (state.pageInfo.pageIndex === 1) {
        closeToast();
      }
    }
  };

  if (immediate) {
    handlerQuery();
  }

  const onLoad = () => {
    (state.pageInfo.pageIndex as number)++;
    console.log(111);

    handlerQuery();
  };

  const onSearch = () => {
    state.pageInfo.pageIndex = 1;
    handlerQuery();
  };

  return {
    list,
    finished,
    ...toRefs(state),
    handlerQuery,
    onLoad,
    onSearch
  };
};
