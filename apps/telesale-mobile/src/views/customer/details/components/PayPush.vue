<script lang="ts" setup>
import { ref } from "vue";
import {
  showLoadingToast,
  showToast,
  closeToast,
  showConfirmDialog
} from "vant";
import { useRoute, useRouter } from "vue-router";
import {
  priceDiffFindNew,
  priceDiffFind,
  repurchaseFind,
  pushPay,
  getHasIpadApi,
  getGoodConfigApi,
  getDiscoveryPageAuthApi
} from "@/api/customer/details";
import MySelect from "@/components/MySelect/index.vue";
import {
  usePayPush,
  schoolYearList,
  ClassTarget,
  newSchoolYearList,
  getStageGood,
  getLabel
} from "@telesale/shared";
import SearchSelect from "@/components/SearchSelect/index.vue";
import CategoryPicker from "../components/CategoryPicker.vue";
import { getAuth } from "@/utils/common/auth";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/store/modules/user";
import {
  isStagesList,
  stagesType
} from "@telesale/shared/src/data/exclusiveLink";
import {
  getCourse<PERSON>ist<PERSON><PERSON>,
  getGoodInfo<PERSON>pi,
  getSyncData<PERSON><PERSON>,
  getTagList<PERSON>pi
} from "@/api/customer/exclusiveLink";
import { findPositionApi } from "@/api/user";
import { isShowLink } from "@telesale/shared/src/utils/business";
import { isExperimentGroupWorkerApi } from "@/api/common";
import { useQianYuan } from "@/hooks/useQianYuan";
import { getNewDiffPriceListApi } from "@/api/customer/diffPrice";
import { newDiffStage } from "@telesale/shared/src/data/customer";
import {
  getDiffSettingApi,
  getVenueLinkGoodsListApi
} from "@/api/customer/linkSetting";
import { useVisibleLink } from "@/hooks/useVisibleLink";

const route = useRoute();
const router = useRouter();
const { userMsg } = storeToRefs(useUserStore());
const { userId, onionid } = route.query as Record<string, string>;

const { loading: showLinkLoading, hasPermissions } = useVisibleLink();

const {
  showList,
  linkFilterData,
  typeList,
  loading,
  bigVipType,
  form,
  bigVipList,
  goodList,
  goodsMobile,
  ipadTypeList,
  showNewList,
  syncData,
  blocksGood,
  goodInfo,
  changeTypeHandle,
  changeStage,
  clearValue,
  changeTarget,
  changeType,
  changeNewStage,
  filterDiff,
  changeBigVip,
  changeGood,
  changeBlockSchoolYear,
  changeblocksGood,
  changeUpgrade,
  changeInstallment,
  handleData
} = usePayPush({
  mode: import.meta.env.MODE,
  userId,
  onionid,
  stageInfo: {
    grade: route.query.grade as string,
    stage: route.query.stage as string
  },
  priceDiffFindNew,
  priceDiffFind,
  repurchaseFind,
  getCourseListApi,
  getGoodConfigApi,
  getNewDiffPriceListApi,
  getDiffSettingApi,
  getGoodInfoApi,
  getSyncDataApi,
  getTagListApi,
  getOrgData: () =>
    findPositionApi({ key: "id", value: userMsg.value.id + "" }),
  isExperimentGroupWorkerApi: () =>
    isExperimentGroupWorkerApi({ workerId: userMsg.value.id as number }),
  getVenueLinkGoodsListApi
});
const { isStages } = storeToRefs(useUserStore());
const isShow = ref<boolean>(false);
const isCategory = ref<boolean>(false);
const targetId = ref<number>();
const targetName = ref<string>("");
const pushDiscover = ref(false);

const search = e => {
  form.target =
    (linkFilterData.value as ClassTarget[]).find(item => item.id === e) || null;
};

const searchNewDiff = e => {
  form.target =
    (showNewList.value as ClassTarget[]).find(item => item.id === e) || null;
};

const changeBigVipType = () => {
  targetId.value = undefined;
  targetName.value = "";
  changeBigVip();
};
const changeNewStageType = () => {
  targetId.value = undefined;
  targetName.value = "";
  changeNewStage();
};

const searchCategory = (e: string) => {
  changeTarget();
  showList.value.forEach(item => {
    item.options.forEach(f => {
      if (f.id === e) {
        form.target = f;
      }
    });
  });
};

const myChangeGood = e => {
  form.goods = e || undefined;
  changeGood(e);
};

const myChangeBlocksGood = e => {
  form.target = e || undefined;
  changeblocksGood();
};

const getAuthPage = () => {
  getDiscoveryPageAuthApi({ userId: userId }).then(res => {
    pushDiscover.value = res.hasAuth;
  });
};

getAuthPage();

watchEffect(() => {
  loading.value ? showLoadingToast({}) : closeToast();
  if (!form.target) {
    targetId.value = undefined;
    targetName.value = "";
  }
});

const onSubmit = () => {
  // showLoadingToast({});
  showConfirmDialog({
    title: "提示",
    message: "确定进行支付推送吗？"
  }).then(async () => {
    loading.value = true;
    const data = handleData();
    data.pushDiscover = pushDiscover.value;
    pushPay(data)
      .then(() => {
        showToast("操作成功");
        setTimeout(() => {
          router.back();
        }, 1500);
      })
      .finally(() => {
        setTimeout(() => {
          loading.value = false;
        }, 1500);
      });
  });
};
</script>

<template>
  <div>
    <div class="bg-#ecf5fd p-10px font-bold mb-10px" v-if="pushDiscover">
      说明：一天只能推送3次，当日推送的所有商品都会展示在APP【我的购买】中，最新的一条商品会同时展示在【发现页】，推送的商品链接当天24点后过期
    </div>
    <van-form class="mt-20px" @submit="onSubmit">
      <van-cell-group inset>
        <van-field v-model="form.onionid" readonly label="洋葱ID" />
        <van-field
          name="pushType"
          label="购买类型"
          required
          :rules="[
            {
              required: true,
              message: '请选择购买类型',
              trigger: 'onChange'
            }
          ]"
        >
          <template #input>
            <van-radio-group
              v-model="form.pushType"
              direction="horizontal"
              @change="changeType"
            >
              <van-radio icon-size="12" name="link">专属链接</van-radio>
              <van-radio
                icon-size="12"
                v-if="
                  getAuth('telesale_admin_new_exclusiveLink_newCreateLink') &&
                  hasPermissions &&
                  !showLinkLoading
                "
                name="blocksGood"
              >
                会场链接（新）
              </van-radio>
              <van-radio
                icon-size="12"
                name="exclusiveLink"
                v-if="
                  getAuth('telesale_admin_new_exclusiveLink_createLink') &&
                  hasPermissions &&
                  !showLinkLoading
                "
              >
                会场链接
              </van-radio>
              <van-radio
                icon-size="12"
                v-auth="'telesale_admin_diff_price'"
                name="difference_v6"
              >
                补差价（新）
              </van-radio>
              <van-radio
                icon-size="12"
                v-auth="'telesale_admin_repurchase'"
                name="repurchase"
              >
                续购
              </van-radio>
              <!-- <van-radio
                icon-size="12"
                v-auth="'telesale_admin_exclusiveLink_discountsCard'"
                name="zhufengPre2024"
              >
                省钱卡
              </van-radio> -->
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-if="form.pushType === 'repurchase' && typeList.length > 0"
          name="type"
          label="续购类型"
          required
          :rules="[
            {
              required: true,
              message: '请选择续购类型',
              trigger: 'onChange'
            }
          ]"
        >
          <template #input>
            <van-radio-group
              v-model="form.type"
              direction="horizontal"
              @change="changeTypeHandle"
            >
              <van-radio
                icon-size="12"
                v-for="item in typeList"
                :key="item.value"
                :name="item.value"
              >
                {{ item.name }}
              </van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <!-- <van-field
          name="isNewExclusiveLink"
          label="是否为会场链接"
          v-show="
            form.pushType === 'link' && hasPermissions && !showLinkLoading
          "
        >
          <template #input>
            <van-switch
              v-model="form.isNewExclusiveLink"
              :size="20"
              @change="clearValue"
            />
          </template>
        </van-field> -->
        <template v-if="form.pushType === 'blocksGood'">
          <van-field
            label="年级"
            name="schoolYear"
            required
            :rules="[
              {
                required: true,
                message: '请选择年级',
                trigger: 'onChange'
              }
            ]"
          >
            <template #input>
              <van-radio-group
                v-model="form.schoolYear"
                direction="horizontal"
                @change="changeBlockSchoolYear"
              >
                <van-radio
                  icon-size="12"
                  v-for="(item, index) in syncData"
                  :key="index"
                  :name="item.schoolYear"
                >
                  {{
                    item.schoolYear === "三年级"
                      ? "一到三年级"
                      : item.schoolYear
                  }}
                </van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <MySelect
            v-model:value="goodsMobile"
            name="picker"
            label="商品"
            placeholder="请选择商品"
            :columns="blocksGood"
            :options="{
              label: 'strategyName',
              value: 'id'
            }"
            required
            :rules="[
              { required: true, message: '请选择商品', trigger: 'onChange' }
            ]"
            @confim="myChangeBlocksGood"
          />
          <van-field
            label="打包购买首购+升单商品"
            name="schoolYear"
            :rules="[
              {
                required: false,
                message: '请选择',
                trigger: 'onChange'
              }
            ]"
            v-if="form.target?.packGood?.length"
          >
            <template #input>
              <van-radio-group
                v-model="form.isPackGood"
                direction="horizontal"
                @change="changeUpgrade"
              >
                <van-radio icon-size="12" :name="false">否</van-radio>
                <van-radio icon-size="12" :name="true">是</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field
            label="加购平板"
            name="addPad"
            :rules="[
              {
                required: false,
                message: '请选择',
                trigger: 'onChange'
              }
            ]"
            v-if="form.target?.addContent?.length"
          >
            <template #input>
              <van-radio-group
                v-model="form.addPad"
                direction="horizontal"
                @change="changeUpgrade"
              >
                <van-radio
                  icon-size="12"
                  v-for="item in form.target.addContent"
                  :key="item.label"
                  :name="item.label"
                >
                  {{ item.name }}
                </van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <template v-if="goodInfo">
            <van-field label="商品原价">
              <template #input>
                ¥ {{ goodInfo.originalAmount?.toFixed(2) }}
              </template>
            </van-field>
            <van-field label="商品售价">
              <template #input>¥ {{ goodInfo.amount?.toFixed(2) }}</template>
            </van-field>
            <van-field label="用户实付价">
              <template #input>
                ¥ {{ (goodInfo.amount - goodInfo.deductAmount)?.toFixed(2) }}
              </template>
            </van-field>
          </template>
        </template>
        <template v-else>
          <template
            v-if="
              !form.isNewExclusiveLink && form.pushType !== 'zhufengPre2024'
            "
          >
            <template v-if="form.pushType === 'difference_v4'">
              <van-field name="type" label="课程类型">
                <template #input>
                  <van-radio-group
                    v-model="bigVipType"
                    direction="horizontal"
                    @change="changeBigVipType"
                  >
                    <van-radio
                      icon-size="12"
                      v-for="item in bigVipList"
                      :key="item.value"
                      :name="item.value"
                    >
                      {{ item.label }}
                    </van-radio>
                  </van-radio-group>
                </template>
              </van-field>
            </template>
            <template v-if="form.pushType === 'difference_v6'">
              <van-field name="type" label="课程类型">
                <template #input>
                  <van-radio-group
                    v-model="bigVipType"
                    direction="horizontal"
                    @change="changeNewStageType"
                  >
                    <van-radio
                      icon-size="12"
                      v-for="item in newDiffStage"
                      :key="item.value"
                      :name="item.value"
                    >
                      {{ item.label }}
                    </van-radio>
                  </van-radio-group>
                </template>
              </van-field>
            </template>
            <template
              v-if="
                form.pushType.indexOf('difference_v4') > -1 ||
                form.type === 'activity'
              "
            >
              <van-field
                label="课程名称"
                v-model="targetName"
                @click="isCategory = true"
                autosize
                placeholder="请搜索课程名称或价格"
                type="textarea"
                rows="1"
                is-link
                required
                readonly
                :rules="[
                  {
                    required: true,
                    message: '请选择课程',
                    trigger: 'onChange'
                  }
                ]"
              />
              <CategoryPicker
                v-if="isCategory"
                v-model:value="targetId"
                v-model:show="isCategory"
                v-model:name="targetName"
                :data="showList"
                title="选择课程"
                :options="{
                  label: 'name',
                  value: 'id'
                }"
                @filterChange="filterDiff"
                @submit="searchCategory"
              />
            </template>
            <template v-else-if="form.pushType === 'difference_v6'">
              <van-field
                label="课程名称"
                v-model="targetName"
                @click="isShow = true"
                placeholder="请选择课程名称"
                is-link
                required
                readonly
                :rules="[
                  {
                    required: true,
                    message: '请选择课程',
                    trigger: 'onChange'
                  }
                ]"
              />
              <SearchSelect
                v-if="isShow"
                v-model:value="targetId"
                v-model:show="isShow"
                v-model:name="targetName"
                :data="showNewList"
                title="选择课程"
                :options="{
                  label: 'nameInit',
                  value: 'id'
                }"
                @submit="searchNewDiff"
              />
            </template>
            <template v-else>
              <van-field
                label="课程名称"
                v-model="targetName"
                @click="isShow = true"
                placeholder="请选择课程名称"
                is-link
                required
                readonly
                :rules="[
                  {
                    required: true,
                    message: '请选择课程',
                    trigger: 'onChange'
                  }
                ]"
              />
              <SearchSelect
                v-if="isShow"
                v-model:value="targetId"
                v-model:show="isShow"
                v-model:name="targetName"
                :data="linkFilterData"
                title="选择课程"
                :options="{
                  label: 'name',
                  value: 'id'
                }"
                @submit="search"
              />
            </template>
          </template>
          <template v-if="form.isNewExclusiveLink">
            <van-field
              label="学段"
              name="stage"
              required
              :rules="[
                {
                  required: true,
                  message: '请选择学段',
                  trigger: 'onChange'
                }
              ]"
            >
              <template #input>
                <van-radio-group
                  v-model="form.stage"
                  direction="horizontal"
                  @change="changeStage"
                >
                  <van-radio
                    icon-size="12"
                    v-for="item in goodList"
                    :key="item.cname"
                    :name="item.cname"
                  >
                    {{ item.name }}
                  </van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <MySelect
              v-model:value="goodsMobile"
              name="picker"
              label="商品"
              placeholder="请选择商品"
              :columns="getStageGood(goodList, form.stage)"
              :options="{
                label: 'name',
                value: 'name'
              }"
              required
              :rules="[
                { required: true, message: '请选择商品', trigger: 'onChange' }
              ]"
              @confim="myChangeGood"
            />
            <van-field
              v-if="form.goods"
              name="exchange"
              label="平板"
              required
              :rules="[
                {
                  required: true,
                  message: '请选择平板',
                  trigger: 'onChange'
                }
              ]"
            >
              <template #input>
                <van-radio-group v-model="form.exchange" direction="horizontal">
                  <van-radio
                    icon-size="12"
                    v-for="item in ipadTypeList"
                    :key="item.value"
                    :name="item.value"
                  >
                    {{ item.label }}
                  </van-radio>
                </van-radio-group>
              </template>
            </van-field>
            <van-field name="amount" label="商品售价" v-if="form.exchange">
              <template #input>
                {{ getLabel(form.exchange, ipadTypeList, "amount", "value") }}
              </template>
            </van-field>
          </template>
          <template v-if="form.pushType !== 'link' && form.target">
            <template v-if="form.pushType === 'difference_v6'">
              <van-field label="商品原价（划线价）" readonly>
                <template #input>
                  {{ form.target?.originalAmount?.toFixed(2) }}
                </template>
              </van-field>
              <van-field label="商品直降" readonly>
                <template #input>
                  {{ form.target?.reducePrice?.toFixed(2) }}
                </template>
              </van-field>
              <van-field label="补差优惠合计" readonly>
                <template #input>
                  {{ form.target?.deductAmount?.toFixed(2) }}
                </template>
              </van-field>
              <van-field label="暑促优惠" readonly>
                <template #input>
                  {{ form.target?.directAmount?.toFixed(2) }}
                </template>
              </van-field>
              <van-field label="实付" readonly>
                <template #input>
                  {{ form.target.diffPrice.toFixed(2) }}
                </template>
              </van-field>
            </template>
            <template v-else>
              <template
                v-if="
                  form.target?.strategyType === 'xinxiActivityBase' ||
                  form.target?.strategyType === 'xinxiActivityPro' ||
                  form.target?.strategyType === '2023double11'
                "
              >
                <MySelect
                  :style="{
                    dispaly:
                      !form.type || form.type === 'activity' ? 'none' : 'block'
                  }"
                  name="schoolYear"
                  label="年级"
                  v-model:value="form.schoolYear"
                  :columns="schoolYearList"
                  placeholder="请选择年级"
                  :options="{
                    label: 'text',
                    value: 'value'
                  }"
                  required
                  :rules="[
                    {
                      required: true,
                      message: '请选择年级',
                      trigger: 'onChange'
                    }
                  ]"
                />
              </template>

              <template v-if="form.pushType === 'repurchase'">
                <van-field
                  v-model="form.target.recommend"
                  readonly
                  label="推荐"
                />
                <van-field
                  v-model="form.target.sellingPoints"
                  readonly
                  label="营销卖点"
                />
                <van-field
                  v-model="form.target.OriginAmount"
                  readonly
                  label="商品原价"
                />
              </template>

              <van-field
                v-model="form.target.amount"
                readonly
                label="商品售价"
              />
              <template v-if="!form.type || form.type === 'activity'">
                <van-field
                  label="折算金额"
                  readonly
                  v-model="form.target.discountPrice"
                />
                <van-field
                  label="实付金额"
                  readonly
                  v-model="form.target.diffPrice"
                />
              </template>
            </template>
          </template>
        </template>

        <template v-if="isStages && form.pushType !== 'zhufengPre2024'">
          <van-field
            name="radio"
            label="分期支付"
            required
            :rules="[
              {
                required: true,
                message: '请选择分期支付',
                trigger: 'onChange'
              }
            ]"
          >
            <template #input>
              <van-radio-group
                v-model="form.isInstallment"
                direction="horizontal"
                @change="changeInstallment"
              >
                <van-radio
                  v-for="(item, index) in isStagesList"
                  :key="index"
                  :name="item.value"
                >
                  {{ item.label }}
                </van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field
            v-if="form.isInstallment === 1"
            name="checkboxGroup"
            label="分期支付方式"
            required
            :rules="[
              {
                required: true,
                message: '请选择分期支付方式',
                trigger: 'onChange'
              }
            ]"
          >
            <template #input>
              <van-checkbox-group
                v-model="form.installmentPayType"
                direction="horizontal"
              >
                <van-checkbox
                  v-for="(item, index) in stagesType"
                  :key="index"
                  :name="item.value"
                  shape="square"
                >
                  {{ item.label }}
                </van-checkbox>
              </van-checkbox-group>
            </template>
          </van-field>
        </template>
      </van-cell-group>
      <div style="margin: 16px">
        <van-button round block type="primary" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<style lang="scss" scoped></style>
